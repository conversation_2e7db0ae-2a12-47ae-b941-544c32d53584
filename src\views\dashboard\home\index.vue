<script setup lang="ts">
import { ref } from 'vue';

defineOptions({
  name: 'Dashboard'
});

const chartData = ref([
  { name: '销售额', value: 12580 },
  { name: '订单数', value: 3456 },
  { name: '用户数', value: 8901 },
  { name: '转化率', value: 23.5 }
]);
</script>

<template>
  <div class="dashboard-grid grid-cols-1 lg:grid-cols-4 md:grid-cols-2 xl:grid-cols-4">
    <!-- 数据卡片 -->
    <NCard v-for="item in chartData" :key="item.name" class="dashboard-card">
      <div class="text-center">
        <div class="dashboard-text-muted mb-8px text-14px">{{ item.name }}</div>
        <div class="dashboard-text-primary text-24px font-600">{{ item.value }}</div>
      </div>
    </NCard>

    <!-- 图表区域 -->
    <NCard class="dashboard-card col-span-full lg:col-span-2">
      <template #header>
        <span class="dashboard-text-primary">趋势图表</span>
      </template>
      <div class="dashboard-text-secondary h-300px flex-center">图表内容区域</div>
    </NCard>

    <NCard class="dashboard-card col-span-full lg:col-span-2">
      <template #header>
        <span class="dashboard-text-primary">数据统计</span>
      </template>
      <div class="dashboard-text-secondary h-300px flex-center">统计内容区域</div>
    </NCard>
  </div>
</template>
