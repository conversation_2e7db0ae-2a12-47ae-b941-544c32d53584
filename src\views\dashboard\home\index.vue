<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NGrid, NGi, NStatistic, NProgress, NIcon } from 'naive-ui';

defineOptions({
  name: 'Dashboard'
});

const isLoaded = ref(false);

const chartData = ref([
  {
    name: '销售额',
    value: 12580,
    unit: '万元',
    icon: 'mdi:currency-usd',
    trend: '+12.5%',
    color: '#52c41a'
  },
  {
    name: '订单数',
    value: 3456,
    unit: '笔',
    icon: 'mdi:shopping',
    trend: '+8.2%',
    color: '#1890ff'
  },
  {
    name: '用户数',
    value: 8901,
    unit: '人',
    icon: 'mdi:account-group',
    trend: '+15.3%',
    color: '#722ed1'
  },
  {
    name: '转化率',
    value: 23.5,
    unit: '%',
    icon: 'mdi:trending-up',
    trend: '+2.1%',
    color: '#fa8c16'
  }
]);

const systemMetrics = ref([
  { name: 'CPU使用率', value: 45, color: '#52c41a' },
  { name: '内存使用率', value: 68, color: '#faad14' },
  { name: '磁盘使用率', value: 32, color: '#52c41a' },
  { name: '网络带宽', value: 78, color: '#ff4d4f' }
]);

const recentActivities = ref([
  { time: '10:30', event: '用户登录', count: 1247 },
  { time: '10:25', event: '订单创建', count: 89 },
  { time: '10:20', event: '支付完成', count: 156 },
  { time: '10:15', event: '数据同步', count: 1 },
  { time: '10:10', event: '系统备份', count: 1 }
]);

onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 300);
});
</script>

<template>
  <div class="dashboard-container" :class="{ 'is-loaded': isLoaded }">
    <!-- 数据概览卡片 -->
    <NGrid :cols="4" :x-gap="20" :y-gap="20" responsive="screen" class="overview-grid">
      <NGi v-for="(item, index) in chartData" :key="item.name" :span="1">
        <NCard class="overview-card" :style="{ animationDelay: `${index * 0.1}s` }">
          <div class="card-content">
            <div class="card-header">
              <div class="card-icon" :style="{ color: item.color }">
                <NIcon size="24">
                  <i :class="item.icon"></i>
                </NIcon>
              </div>
              <div class="card-trend" :style="{ color: item.color }">
                {{ item.trend }}
              </div>
            </div>
            <div class="card-body">
              <div class="card-title">{{ item.name }}</div>
              <div class="card-value">
                <NStatistic :value="item.value" :precision="item.name === '转化率' ? 1 : 0">
                  <template #suffix>{{ item.unit }}</template>
                </NStatistic>
              </div>
            </div>
          </div>
        </NCard>
      </NGi>
    </NGrid>

    <!-- 主要内容区域 -->
    <NGrid :cols="3" :x-gap="20" :y-gap="20" responsive="screen" class="main-grid">
      <!-- 图表区域 -->
      <NGi :span="2">
        <NCard class="chart-card">
          <template #header>
            <div class="card-header-content">
              <span class="card-title">数据趋势</span>
              <div class="card-actions">
                <NIcon size="16" class="action-icon">
                  <i class="mdi:refresh"></i>
                </NIcon>
                <NIcon size="16" class="action-icon">
                  <i class="mdi:fullscreen"></i>
                </NIcon>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <NIcon size="48" class="chart-icon">
                <i class="mdi:chart-line"></i>
              </NIcon>
              <div class="chart-text">图表内容区域</div>
            </div>
          </div>
        </NCard>
      </NGi>

      <!-- 系统监控 -->
      <NGi :span="1">
        <NCard class="monitor-card">
          <template #header>
            <span class="card-title">系统监控</span>
          </template>
          <div class="monitor-content">
            <div v-for="metric in systemMetrics" :key="metric.name" class="metric-item">
              <div class="metric-header">
                <span class="metric-name">{{ metric.name }}</span>
                <span class="metric-value">{{ metric.value }}%</span>
              </div>
              <NProgress
                :percentage="metric.value"
                :color="metric.color"
                :show-indicator="false"
                :height="6"
                :border-radius="3"
              />
            </div>
          </div>
        </NCard>
      </NGi>
    </NGrid>

    <!-- 底部区域 -->
    <NGrid :cols="2" :x-gap="20" :y-gap="20" responsive="screen" class="bottom-grid">
      <!-- 实时活动 -->
      <NGi :span="1">
        <NCard class="activity-card">
          <template #header>
            <span class="card-title">实时活动</span>
          </template>
          <div class="activity-content">
            <div v-for="activity in recentActivities" :key="activity.time" class="activity-item">
              <div class="activity-time">{{ activity.time }}</div>
              <div class="activity-event">{{ activity.event }}</div>
              <div class="activity-count">{{ activity.count }}</div>
            </div>
          </div>
        </NCard>
      </NGi>

      <!-- 数据统计 -->
      <NGi :span="1">
        <NCard class="stats-card">
          <template #header>
            <span class="card-title">数据统计</span>
          </template>
          <div class="stats-content">
            <div class="stats-placeholder">
              <NIcon size="48" class="stats-icon">
                <i class="mdi:chart-pie"></i>
              </NIcon>
              <div class="stats-text">统计图表区域</div>
            </div>
          </div>
        </NCard>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped>
.dashboard-container {
  padding: 0;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.dashboard-container.is-loaded {
  opacity: 1;
  transform: translateY(0);
}

.overview-grid {
  margin-bottom: 20px;
}

.main-grid {
  margin-bottom: 20px;
}

.bottom-grid {
  margin-bottom: 0;
}

/* 概览卡片 */
.overview-card {
  height: 120px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(14, 66, 210, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out both;
}

.overview-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(14, 66, 210, 0.2);
  border-color: rgba(14, 66, 210, 0.5);
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-icon {
  padding: 8px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
}

.card-trend {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
}

/* 图表卡片 */
.chart-card {
  height: 400px;
  animation: slideInLeft 0.6s ease-out 0.2s both;
}

.card-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: color 0.3s ease;
}

.action-icon:hover {
  color: #ffffff;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.5);
}

.chart-icon {
  opacity: 0.6;
}

.chart-text {
  font-size: 16px;
}

/* 监控卡片 */
.monitor-card {
  height: 400px;
  animation: slideInRight 0.6s ease-out 0.3s both;
}

.monitor-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 320px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-name {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

/* 活动卡片 */
.activity-card {
  height: 300px;
  animation: slideInUp 0.6s ease-out 0.4s both;
}

.activity-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 220px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 12px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(14, 66, 210, 0.1);
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(14, 66, 210, 0.3);
}

.activity-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  min-width: 40px;
}

.activity-event {
  flex: 1;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.activity-count {
  font-size: 12px;
  font-weight: 600;
  color: #0E42D2;
  padding: 2px 8px;
  border-radius: 4px;
  background: rgba(14, 66, 210, 0.1);
}

/* 统计卡片 */
.stats-card {
  height: 300px;
  animation: slideInUp 0.6s ease-out 0.5s both;
}

.stats-content {
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.5);
}

.stats-icon {
  opacity: 0.6;
}

.stats-text {
  font-size: 16px;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-grid {
    grid-template-columns: 1fr;
  }

  .bottom-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-container {
    padding: 0 10px;
  }
}
</style>
