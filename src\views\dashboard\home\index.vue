<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NCard, NStatistic, NProgress, NIcon } from 'naive-ui';

defineOptions({
  name: 'Dashboard'
});

const isLoaded = ref(false);

const chartData = ref([
  {
    name: '销售额',
    value: 12580,
    unit: '万元',
    icon: 'mdi:currency-usd',
    trend: '+12.5%',
    color: '#52c41a'
  },
  {
    name: '订单数',
    value: 3456,
    unit: '笔',
    icon: 'mdi:shopping',
    trend: '+8.2%',
    color: '#1890ff'
  },
  {
    name: '用户数',
    value: 8901,
    unit: '人',
    icon: 'mdi:account-group',
    trend: '+15.3%',
    color: '#722ed1'
  },
  {
    name: '转化率',
    value: 23.5,
    unit: '%',
    icon: 'mdi:trending-up',
    trend: '+2.1%',
    color: '#fa8c16'
  }
]);

const systemMetrics = ref([
  { name: 'CPU使用率', value: 45, color: '#52c41a' },
  { name: '内存使用率', value: 68, color: '#faad14' },
  { name: '磁盘使用率', value: 32, color: '#52c41a' },
  { name: '网络带宽', value: 78, color: '#ff4d4f' }
]);

const recentActivities = ref([
  { time: '10:30', event: '用户登录', count: 1247 },
  { time: '10:25', event: '订单创建', count: 89 },
  { time: '10:20', event: '支付完成', count: 156 },
  { time: '10:15', event: '数据同步', count: 1 },
  { time: '10:10', event: '系统备份', count: 1 }
]);

onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 300);
});
</script>

<template>
  <div class="dashboard-container" :class="{ 'is-loaded': isLoaded }">
    <!-- 左侧区域：3个容器 -->
    <div class="dashboard-grid">
      <div class="left-column">
        <!-- 左侧容器1：数据概览 -->
        <NCard class="dashboard-card overview-card">
          <template #header>
            <span class="card-title">数据概览</span>
          </template>
          <div class="overview-content">
            <div v-for="(item, index) in chartData.slice(0, 2)" :key="item.name" class="overview-item" :style="{ animationDelay: `${index * 0.1}s` }">
              <div class="overview-icon" :style="{ color: item.color }">
                <NIcon size="20">
                  <i :class="item.icon"></i>
                </NIcon>
              </div>
              <div class="overview-info">
                <div class="overview-label">{{ item.name }}</div>
                <div class="overview-value">
                  <NStatistic :value="item.value" :precision="item.name === '转化率' ? 1 : 0">
                    <template #suffix>{{ item.unit }}</template>
                  </NStatistic>
                </div>
                <div class="overview-trend" :style="{ color: item.color }">{{ item.trend }}</div>
              </div>
            </div>
          </div>
        </NCard>

        <!-- 左侧容器2：系统监控 -->
        <NCard class="dashboard-card monitor-card">
          <template #header>
            <span class="card-title">系统监控</span>
          </template>
          <div class="monitor-content">
            <div v-for="metric in systemMetrics" :key="metric.name" class="metric-item">
              <div class="metric-header">
                <span class="metric-name">{{ metric.name }}</span>
                <span class="metric-value">{{ metric.value }}%</span>
              </div>
              <NProgress
                :percentage="metric.value"
                :color="metric.color"
                :show-indicator="false"
                :height="6"
                :border-radius="3"
              />
            </div>
          </div>
        </NCard>

        <!-- 左侧容器3：实时活动 -->
        <NCard class="dashboard-card activity-card">
          <template #header>
            <span class="card-title">实时活动</span>
          </template>
          <div class="activity-content">
            <div v-for="activity in recentActivities.slice(0, 4)" :key="activity.time" class="activity-item">
              <div class="activity-time">{{ activity.time }}</div>
              <div class="activity-event">{{ activity.event }}</div>
              <div class="activity-count">{{ activity.count }}</div>
            </div>
          </div>
        </NCard>
      </div>

      <!-- 中间区域：2个容器 -->
      <div class="center-column">
        <!-- 中间容器1：主要图表 -->
        <NCard class="dashboard-card chart-card">
          <template #header>
            <div class="card-header-content">
              <span class="card-title">数据趋势</span>
              <div class="card-actions">
                <NIcon size="16" class="action-icon">
                  <i class="mdi:refresh"></i>
                </NIcon>
                <NIcon size="16" class="action-icon">
                  <i class="mdi:fullscreen"></i>
                </NIcon>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              <NIcon size="48" class="chart-icon">
                <i class="mdi:chart-line"></i>
              </NIcon>
              <div class="chart-text">主要图表区域</div>
            </div>
          </div>
        </NCard>

        <!-- 中间容器2：数据分析 -->
        <NCard class="dashboard-card analysis-card">
          <template #header>
            <span class="card-title">数据分析</span>
          </template>
          <div class="analysis-content">
            <div class="analysis-placeholder">
              <NIcon size="48" class="analysis-icon">
                <i class="mdi:chart-bar"></i>
              </NIcon>
              <div class="analysis-text">分析图表区域</div>
            </div>
          </div>
        </NCard>
      </div>

      <!-- 右侧区域：3个容器 -->
      <div class="right-column">
        <!-- 右侧容器1：关键指标 -->
        <NCard class="dashboard-card metrics-card">
          <template #header>
            <span class="card-title">关键指标</span>
          </template>
          <div class="metrics-content">
            <div v-for="(item, index) in chartData.slice(2, 4)" :key="item.name" class="metrics-item" :style="{ animationDelay: `${(index + 2) * 0.1}s` }">
              <div class="metrics-icon" :style="{ color: item.color }">
                <NIcon size="20">
                  <i :class="item.icon"></i>
                </NIcon>
              </div>
              <div class="metrics-info">
                <div class="metrics-label">{{ item.name }}</div>
                <div class="metrics-value">
                  <NStatistic :value="item.value" :precision="item.name === '转化率' ? 1 : 0">
                    <template #suffix>{{ item.unit }}</template>
                  </NStatistic>
                </div>
                <div class="metrics-trend" :style="{ color: item.color }">{{ item.trend }}</div>
              </div>
            </div>
          </div>
        </NCard>

        <!-- 右侧容器2：统计图表 -->
        <NCard class="dashboard-card stats-card">
          <template #header>
            <span class="card-title">统计图表</span>
          </template>
          <div class="stats-content">
            <div class="stats-placeholder">
              <NIcon size="48" class="stats-icon">
                <i class="mdi:chart-pie"></i>
              </NIcon>
              <div class="stats-text">统计图表区域</div>
            </div>
          </div>
        </NCard>

        <!-- 右侧容器3：快速操作 -->
        <NCard class="dashboard-card actions-card">
          <template #header>
            <span class="card-title">快速操作</span>
          </template>
          <div class="actions-content">
            <div class="action-grid">
              <div class="action-item">
                <NIcon size="24" class="action-item-icon">
                  <i class="mdi:download"></i>
                </NIcon>
                <span class="action-item-text">导出数据</span>
              </div>
              <div class="action-item">
                <NIcon size="24" class="action-item-icon">
                  <i class="mdi:printer"></i>
                </NIcon>
                <span class="action-item-text">打印报表</span>
              </div>
              <div class="action-item">
                <NIcon size="24" class="action-item-icon">
                  <i class="mdi:share"></i>
                </NIcon>
                <span class="action-item-text">分享链接</span>
              </div>
              <div class="action-item">
                <NIcon size="24" class="action-item-icon">
                  <i class="mdi:cog"></i>
                </NIcon>
                <span class="action-item-text">配置面板</span>
              </div>
            </div>
          </div>
        </NCard>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-container {
  height: 100%;
  padding: 0;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.dashboard-container.is-loaded {
  opacity: 1;
  transform: translateY(0);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 24px;
  height: 100%;
  max-height: calc(100vh - 180px); /* 减去头部和页脚高度 */
}

.left-column,
.center-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
}

.dashboard-card {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(24, 144, 255, 0.3);
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out both;
  flex: 1;
  min-height: 0;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.1);
}

.dashboard-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(24, 144, 255, 0.3);
  border-color: rgba(24, 144, 255, 0.5);
  background: rgba(255, 255, 255, 0.12);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #ffffff, #91d5ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 左侧列样式 */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 16px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.overview-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(24, 144, 255, 0.4);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

.overview-icon {
  padding: 10px;
  border-radius: 10px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.2), rgba(64, 169, 255, 0.3));
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.overview-info {
  flex: 1;
}

.overview-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 6px;
  font-weight: 500;
}

.overview-value {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.overview-trend {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 6px;
  background: rgba(24, 144, 255, 0.2);
}

/* 监控卡片 */
.monitor-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  overflow-y: auto;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.metric-name {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
}

/* 活动卡片 */
.activity-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 10px 14px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.15);
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(24, 144, 255, 0.3);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.activity-time {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  min-width: 38px;
  font-weight: 500;
}

.activity-event {
  flex: 1;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.activity-count {
  font-size: 11px;
  font-weight: 600;
  color: #1890ff;
  padding: 3px 8px;
  border-radius: 6px;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.2), rgba(64, 169, 255, 0.3));
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

/* 中间列样式 */
.chart-card {
  animation: slideInLeft 0.6s ease-out 0.2s both;
}

.card-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.action-icon {
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px;
  border-radius: 6px;
}

.action-icon:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  transform: scale(1.1);
}

.chart-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.5);
}

.chart-icon {
  opacity: 0.6;
}

.chart-text {
  font-size: 16px;
}

.analysis-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.analysis-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.5);
}

.analysis-icon {
  opacity: 0.6;
}

.analysis-text {
  font-size: 16px;
}

/* 右侧列样式 */
.metrics-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.metrics-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(14, 66, 210, 0.1);
  transition: all 0.3s ease;
}

.metrics-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(14, 66, 210, 0.3);
}

.metrics-icon {
  padding: 8px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
}

.metrics-info {
  flex: 1;
}

.metrics-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 4px;
}

.metrics-value {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 2px;
}

.metrics-trend {
  font-size: 11px;
  font-weight: 500;
}

.stats-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.5);
}

.stats-icon {
  opacity: 0.6;
}

.stats-text {
  font-size: 16px;
}

.actions-content {
  height: 100%;
  padding: 16px;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  height: 100%;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 18px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-item:hover {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.15), rgba(64, 169, 255, 0.2));
  border-color: rgba(24, 144, 255, 0.4);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
}

.action-item-icon {
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.action-item:hover .action-item-icon {
  color: #1890ff;
  transform: scale(1.1);
}

.action-item-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  transition: color 0.3s ease;
  font-weight: 500;
}

.action-item:hover .action-item-text {
  color: #ffffff;
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1.5fr 1fr;
  }
}

@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }

  .left-column,
  .center-column,
  .right-column {
    flex-direction: row;
    height: auto;
  }

  .left-column .dashboard-card,
  .center-column .dashboard-card,
  .right-column .dashboard-card {
    flex: 1;
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .dashboard-grid {
    gap: 10px;
  }

  .left-column,
  .center-column,
  .right-column {
    flex-direction: column;
  }

  .dashboard-container {
    padding: 0 10px;
  }

  .dashboard-card {
    min-height: 250px;
  }
}
</style>
