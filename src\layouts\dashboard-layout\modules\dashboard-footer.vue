<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NIcon, NTooltip } from 'naive-ui';

defineOptions({
  name: 'DashboardFooter'
});

const systemStatus = ref({
  cpu: 45,
  memory: 68,
  network: 'normal',
  onlineUsers: 1247
});

const currentYear = new Date().getFullYear();

// 模拟系统状态更新
const updateSystemStatus = () => {
  systemStatus.value = {
    cpu: Math.floor(Math.random() * 100),
    memory: Math.floor(Math.random() * 100),
    network: Math.random() > 0.1 ? 'normal' : 'warning',
    onlineUsers: Math.floor(Math.random() * 2000) + 1000
  };
};

onMounted(() => {
  // 每30秒更新一次系统状态
  setInterval(updateSystemStatus, 30000);
});

const getStatusColor = (value: number) => {
  if (value < 50) return '#52c41a';
  if (value < 80) return '#faad14';
  return '#ff4d4f';
};

const getNetworkStatusColor = (status: string) => {
  return status === 'normal' ? '#52c41a' : '#faad14';
};
</script>

<template>
  <div class="dashboard-footer">
    <!-- 左侧版权信息 -->
    <div class="footer-left">
      <div class="copyright">
        <span>© {{ currentYear }} 数据大屏系统</span>
        <span class="separator">|</span>
        <span>Powered by Soybean Admin</span>
      </div>
    </div>

    <!-- 中间系统状态 -->
    <div class="footer-center">
      <div class="status-group">
        <NTooltip trigger="hover">
          <template #trigger>
            <div class="status-item">
              <NIcon size="16" class="status-icon">
                <i class="mdi:cpu-64-bit"></i>
              </NIcon>
              <span class="status-label">CPU</span>
              <span 
                class="status-value"
                :style="{ color: getStatusColor(systemStatus.cpu) }"
              >
                {{ systemStatus.cpu }}%
              </span>
            </div>
          </template>
          CPU使用率: {{ systemStatus.cpu }}%
        </NTooltip>

        <NTooltip trigger="hover">
          <template #trigger>
            <div class="status-item">
              <NIcon size="16" class="status-icon">
                <i class="mdi:memory"></i>
              </NIcon>
              <span class="status-label">内存</span>
              <span 
                class="status-value"
                :style="{ color: getStatusColor(systemStatus.memory) }"
              >
                {{ systemStatus.memory }}%
              </span>
            </div>
          </template>
          内存使用率: {{ systemStatus.memory }}%
        </NTooltip>

        <NTooltip trigger="hover">
          <template #trigger>
            <div class="status-item">
              <NIcon size="16" class="status-icon">
                <i class="mdi:network"></i>
              </NIcon>
              <span class="status-label">网络</span>
              <span 
                class="status-value"
                :style="{ color: getNetworkStatusColor(systemStatus.network) }"
              >
                {{ systemStatus.network === 'normal' ? '正常' : '警告' }}
              </span>
            </div>
          </template>
          网络状态: {{ systemStatus.network === 'normal' ? '正常' : '警告' }}
        </NTooltip>
      </div>
    </div>

    <!-- 右侧在线用户 -->
    <div class="footer-right">
      <div class="online-users">
        <NIcon size="16" class="user-icon">
          <i class="mdi:account-multiple"></i>
        </NIcon>
        <span class="user-label">在线用户</span>
        <span class="user-count">{{ systemStatus.onlineUsers.toLocaleString() }}</span>
        <div class="pulse-dot"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard-footer {
  height: 50px;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(14, 66, 210, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: relative;
  z-index: 50;
}

.footer-left {
  display: flex;
  align-items: center;
}

.copyright {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.separator {
  color: rgba(255, 255, 255, 0.3);
}

.footer-center {
  display: flex;
  align-items: center;
}

.status-group {
  display: flex;
  align-items: center;
  gap: 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}

.status-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.status-icon {
  color: rgba(255, 255, 255, 0.7);
}

.status-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.status-value {
  font-size: 12px;
  font-weight: 600;
}

.footer-right {
  display: flex;
  align-items: center;
}

.online-users {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 6px;
  background: rgba(14, 66, 210, 0.1);
  border: 1px solid rgba(14, 66, 210, 0.3);
  position: relative;
}

.user-icon {
  color: #52c41a;
}

.user-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.user-count {
  font-size: 12px;
  font-weight: 600;
  color: #52c41a;
}

.pulse-dot {
  width: 6px;
  height: 6px;
  background: #52c41a;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-footer {
  animation: slideInFromBottom 0.6s ease-out 0.3s both;
}
</style>
