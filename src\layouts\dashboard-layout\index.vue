<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { NConfigProvider } from 'naive-ui';
import { dashboardTheme } from '@/theme/dashboard-theme';
import SystemLogo from '@/components/common/system-logo.vue';
import GlobalContent from '../modules/global-content/index.vue';

defineOptions({
  name: 'DashboardLayout'
});

const currentTime = ref('');

// 更新时间
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);
});
</script>

<template>
  <NConfigProvider :theme-overrides="dashboardTheme">
    <div class="dashboard-layout">
      <!-- 顶部标题栏 -->
      <div class="dashboard-header">
        <div class="flex-y-center justify-between px-24px">
          <div class="flex-y-center gap-16px">
            <SystemLogo class="text-30px text-primary" />
            <h1 class="dashboard-title">数据大屏</h1>
          </div>
          <div class="flex-y-center gap-16px text-white">
            <div class="current-time">{{ currentTime }}</div>
            <slot name="header-actions"></slot>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="dashboard-content">
        <GlobalContent :show-padding="false" />
      </div>
    </div>
  </NConfigProvider>
</template>

<style scoped>
.dashboard-layout {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  height: 80px;
  background: linear-gradient(90deg, rgba(14, 66, 210, 0.8) 0%, rgba(26, 35, 50, 0.9) 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  position: relative;
  z-index: 100;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.current-time {
  font-size: 16px;
  font-family: 'Courier New', monospace;
  color: rgba(255, 255, 255, 0.9);
}

.dashboard-content {
  flex: 1;
  overflow: hidden;
}
</style>
