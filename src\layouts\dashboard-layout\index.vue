<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useFullscreen } from '@vueuse/core';
import { NButton, NConfigProvider, NIcon, NTooltip } from 'naive-ui';
import { dashboardTheme } from '@/theme/dashboard-theme';
import SystemLogo from '@/components/common/system-logo.vue';
import GlobalContent from '../modules/global-content/index.vue';
import DashboardSettings from './modules/dashboard-settings.vue';

defineOptions({
  name: 'DashboardLayout'
});

const router = useRouter();
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen();

const currentTime = ref('');
const isAnimating = ref(true);
const showSettings = ref(false);
const currentYear = new Date().getFullYear();

const navigationItems = ref([
  { key: 'overview', label: '总览', icon: 'mdi:view-dashboard' },
  { key: 'analytics', label: '分析', icon: 'mdi:chart-line' },
  { key: 'monitor', label: '监控', icon: 'mdi:monitor' },
  { key: 'reports', label: '报表', icon: 'mdi:file-chart' }
]);

const activeKey = ref('overview');

// 更新时间
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 导航菜单选择
const handleMenuSelect = (key: string) => {
  activeKey.value = key;
  // 这里可以添加路由跳转逻辑
};

// 返回主页
const goBack = () => {
  router.push('/home');
};

// 刷新数据
const refreshData = () => {
  window.location.reload();
};

// 打开设置
const openSettings = () => {
  showSettings.value = true;
};

// 生成点样式
const getDotStyle = (_index: number) => {
  const size = Math.random() * 3 + 1;
  const left = Math.random() * 100;
  const top = Math.random() * 100;
  const animationDelay = Math.random() * 5;

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    top: `${top}%`,
    animationDelay: `${animationDelay}s`
  };
};

// 生成线条样式
const getLineStyle = (index: number) => {
  const rotation = (index * 45) % 360;
  const animationDelay = index * 0.5;

  return {
    transform: `rotate(${rotation}deg)`,
    animationDelay: `${animationDelay}s`
  };
};

onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);

  // 页面进入动画
  setTimeout(() => {
    isAnimating.value = false;
  }, 100);
});
</script>

<template>
  <NConfigProvider :theme-overrides="dashboardTheme">
    <div class="dashboard-layout" :class="{ 'is-animating': isAnimating }">
      <!-- 合并的顶部标题栏和导航栏 -->
      <div class="dashboard-header">
        <div class="header-content">
          <!-- 左侧：导航菜单 -->
          <div class="header-left">
            <div class="nav-menu">
              <div
                v-for="item in navigationItems"
                :key="item.key"
                class="nav-item"
                :class="{ active: activeKey === item.key }"
                @click="handleMenuSelect(item.key)"
              >
                <div class="nav-item-content">
                  <i :class="item.icon" class="nav-icon"></i>
                  <span class="nav-label">{{ item.label }}</span>
                </div>
                <div class="nav-indicator"></div>
              </div>
            </div>
          </div>

          <!-- 中间：Logo + 标题 -->
          <div class="header-center">
            <SystemLogo class="logo" />
            <h1 class="dashboard-title">智能数据大屏</h1>
            <div class="title-subtitle">INTELLIGENT DATA DASHBOARD</div>
          </div>

          <!-- 右侧：时间 + 操作按钮 -->
          <div class="header-right">
            <div class="current-time">
              <div class="time-label">当前时间</div>
              <div class="time-value">{{ currentTime }}</div>
            </div>

            <!-- 操作按钮组 -->
            <div class="action-buttons">
              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton quaternary circle size="large" class="action-btn" @click="refreshData">
                    <template #icon>
                      <NIcon size="18">
                        <i class="mdi:refresh"></i>
                      </NIcon>
                    </template>
                  </NButton>
                </template>
                刷新数据
              </NTooltip>

              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton quaternary circle size="large" class="action-btn" @click="openSettings">
                    <template #icon>
                      <NIcon size="18">
                        <i class="mdi:cog"></i>
                      </NIcon>
                    </template>
                  </NButton>
                </template>
                设置
              </NTooltip>

              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton quaternary circle size="large" class="action-btn" @click="toggleFullscreen">
                    <template #icon>
                      <NIcon size="18">
                        <i :class="isFullscreen ? 'mdi:fullscreen-exit' : 'mdi:fullscreen'"></i>
                      </NIcon>
                    </template>
                  </NButton>
                </template>
                {{ isFullscreen ? '退出全屏' : '全屏显示' }}
              </NTooltip>

              <NTooltip trigger="hover">
                <template #trigger>
                  <NButton quaternary circle size="large" class="action-btn" @click="goBack">
                    <template #icon>
                      <NIcon size="18">
                        <i class="mdi:arrow-left"></i>
                      </NIcon>
                    </template>
                  </NButton>
                </template>
                返回主页
              </NTooltip>
            </div>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="dashboard-content">
        <div class="content-wrapper">
          <GlobalContent :show-padding="false" />
        </div>
      </div>

      <!-- 简化的页脚 -->
      <div class="dashboard-footer">
        <div class="footer-content">
          <span>© {{ currentYear }} 智能数据大屏系统 | Powered by Soybean Admin</span>
        </div>
      </div>

      <!-- 蓝色主题背景装饰 -->
      <div class="bg-decoration">
        <div class="bg-waves">
          <div class="wave wave-1"></div>
          <div class="wave wave-2"></div>
          <div class="wave wave-3"></div>
        </div>
        <div class="bg-dots">
          <div v-for="i in 50" :key="i" class="dot" :style="getDotStyle(i)"></div>
        </div>
        <div class="bg-lines">
          <div v-for="i in 8" :key="i" class="line" :style="getLineStyle(i)"></div>
        </div>
      </div>

      <!-- 设置对话框 -->
      <DashboardSettings v-model:visible="showSettings" />
    </div>
  </NConfigProvider>
</template>

<style scoped>
.bg-layout {
  background: none;
}
.dashboard-layout {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #001529 0%, #002766 25%, #003d82 50%, #0050b3 75%, #1890ff 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.6s ease;
}

.dashboard-layout.is-animating {
  opacity: 0;
  transform: scale(0.95);
}

.dashboard-header {
  height: 90px;
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.95) 0%, rgba(0, 39, 102, 0.9) 50%, rgba(24, 144, 255, 0.8) 100%);
  backdrop-filter: blur(20px);
  border-bottom: 2px solid rgba(24, 144, 255, 0.3);
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.2);
  position: relative;
  z-index: 100;
  animation: slideInFromTop 0.8s ease-out;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.header-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 2;
  text-align: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  justify-content: flex-end;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-item {
  position: relative;
  padding: 10px 18px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.nav-item:hover {
  background: rgba(24, 144, 255, 0.15);
  border-color: rgba(24, 144, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.3), rgba(0, 80, 179, 0.4));
  border-color: rgba(24, 144, 255, 0.6);
  color: #ffffff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
}

.nav-item.active .nav-indicator {
  transform: scaleX(1);
}

.nav-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.nav-icon {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #ffffff;
}

.nav-label {
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.nav-item.active .nav-label {
  color: #ffffff;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  border-radius: 1px;
}

.logo {
  font-size: 36px;
  color: #1890ff;
  filter: drop-shadow(0 0 15px rgba(24, 144, 255, 0.6));
  margin-bottom: 4px;
}

.dashboard-title {
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #ffffff, #91d5ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  letter-spacing: 1px;
}

.title-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 2px;
  margin-top: 2px;
  font-weight: 300;
}

.current-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(24, 144, 255, 0.3);
  backdrop-filter: blur(10px);
}

.time-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
}

.time-value {
  font-size: 14px;
  font-family: 'Courier New', monospace;
  color: #ffffff;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.action-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(24, 144, 255, 0.2) !important;
}

.action-btn:hover {
  color: #ffffff !important;
  background: rgba(24, 144, 255, 0.2) !important;
  border-color: rgba(24, 144, 255, 0.4) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.dashboard-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  z-index: 10;
  margin: 0 20px;
}

.content-wrapper {
  height: 100%;
  padding: 24px;
  animation: fadeInUp 0.8s ease-out 0.4s both;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.dashboard-footer {
  height: 50px;
  background: rgba(0, 21, 41, 0.9);
  backdrop-filter: blur(15px);
  border-top: 1px solid rgba(24, 144, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 100;
  animation: slideInFromBottom 0.8s ease-out 0.6s both;
}

.footer-content {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  text-align: center;
}

/* 蓝色主题背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.bg-waves {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(ellipse at center, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: waveFloat linear infinite;
}

.wave-1 {
  top: -50%;
  left: -50%;
  animation-duration: 20s;
  animation-delay: 0s;
}

.wave-2 {
  top: -30%;
  right: -50%;
  animation-duration: 25s;
  animation-delay: -5s;
}

.wave-3 {
  bottom: -50%;
  left: -30%;
  animation-duration: 30s;
  animation-delay: -10s;
}

.bg-dots {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.dot {
  position: absolute;
  background: rgba(24, 144, 255, 0.4);
  border-radius: 50%;
  animation: dotPulse 3s ease-in-out infinite;
}

.bg-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.line {
  position: absolute;
  width: 1px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, rgba(24, 144, 255, 0.2), transparent);
  left: 50%;
  top: 0;
  transform-origin: center;
  animation: lineRotate 15s linear infinite;
}

/* 蓝色主题动画效果 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes waveFloat {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes dotPulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes lineRotate {
  0% {
    transform: rotate(0deg);
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0.3;
  }
}
</style>
