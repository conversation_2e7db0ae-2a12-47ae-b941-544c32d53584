<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { NConfigProvider } from 'naive-ui';
import { dashboardTheme } from '@/theme/dashboard-theme';
import SystemLogo from '@/components/common/system-logo.vue';
import GlobalContent from '../modules/global-content/index.vue';
import DashboardNavigation from './modules/dashboard-navigation.vue';
import DashboardFooter from './modules/dashboard-footer.vue';

defineOptions({
  name: 'DashboardLayout'
});

const currentTime = ref('');
const isAnimating = ref(true);

// 更新时间
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 生成粒子样式
const getParticleStyle = (_index: number) => {
  const size = Math.random() * 4 + 2;
  const left = Math.random() * 100;
  const animationDelay = Math.random() * 20;
  const animationDuration = Math.random() * 10 + 10;

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  };
};

onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);

  // 页面进入动画
  setTimeout(() => {
    isAnimating.value = false;
  }, 100);
});
</script>

<template>
  <NConfigProvider :theme-overrides="dashboardTheme">
    <div class="dashboard-layout" :class="{ 'is-animating': isAnimating }">
      <!-- 顶部标题栏 -->
      <div class="dashboard-header">
        <div class="header-content">
          <div class="header-left">
            <SystemLogo class="logo" />
            <h1 class="dashboard-title">数据大屏</h1>
          </div>
          <div class="header-right">
            <div class="current-time">{{ currentTime }}</div>
            <slot name="header-actions"></slot>
          </div>
        </div>
      </div>

      <!-- 导航区域 -->
      <DashboardNavigation />

      <!-- 内容区域 -->
      <div class="dashboard-content">
        <div class="content-wrapper">
          <GlobalContent :show-padding="false" />
        </div>
      </div>

      <!-- 页脚区域 -->
      <DashboardFooter />

      <!-- 背景装饰 -->
      <div class="bg-decoration">
        <div class="bg-grid"></div>
        <div class="bg-particles">
          <div v-for="i in 20" :key="i" class="particle" :style="getParticleStyle(i)"></div>
        </div>
      </div>
    </div>
  </NConfigProvider>
</template>

<style scoped>
.dashboard-layout {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  color: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.6s ease;
}

.dashboard-layout.is-animating {
  opacity: 0;
  transform: scale(0.95);
}

.dashboard-header {
  height: 80px;
  background: linear-gradient(90deg, rgba(14, 66, 210, 0.8) 0%, rgba(26, 35, 50, 0.9) 100%);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(14, 66, 210, 0.3);
  position: relative;
  z-index: 100;
  animation: slideInFromTop 0.8s ease-out;
}

.header-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo {
  font-size: 30px;
  color: #0e42d2;
  filter: drop-shadow(0 0 10px rgba(14, 66, 210, 0.5));
}

.dashboard-title {
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #ffffff, #a8c8ff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.current-time {
  font-size: 16px;
  font-family: 'Courier New', monospace;
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(14, 66, 210, 0.3);
}

.dashboard-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  z-index: 10;
}

.content-wrapper {
  height: 100%;
  padding: 20px;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.bg-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(14, 66, 210, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(14, 66, 210, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

.bg-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: radial-gradient(circle, rgba(14, 66, 210, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: float linear infinite;
}

/* 动画效果 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}
</style>
