<script setup lang="ts">
import { ref } from 'vue';
import { NButton, NDropdown, NIcon, NTooltip } from 'naive-ui';
import { useRouter } from 'vue-router';
import { useFullscreen } from '@vueuse/core';

defineOptions({
  name: 'DashboardNavigation'
});

const router = useRouter();
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen();

const navigationItems = ref([
  { key: 'overview', label: '总览', icon: 'mdi:view-dashboard' },
  { key: 'analytics', label: '分析', icon: 'mdi:chart-line' },
  { key: 'monitor', label: '监控', icon: 'mdi:monitor' },
  { key: 'reports', label: '报表', icon: 'mdi:file-chart' }
]);

const activeKey = ref('overview');

const handleMenuSelect = (key: string) => {
  activeKey.value = key;
  // 这里可以添加路由跳转逻辑
};

const goBack = () => {
  router.push('/home');
};

const refreshData = () => {
  // 刷新数据逻辑
  window.location.reload();
};
</script>

<template>
  <div class="dashboard-navigation">
    <!-- 左侧导航菜单 -->
    <div class="nav-left">
      <div class="nav-menu">
        <div
          v-for="item in navigationItems"
          :key="item.key"
          class="nav-item"
          :class="{ active: activeKey === item.key }"
          @click="handleMenuSelect(item.key)"
        >
          <div class="nav-item-content">
            <i :class="item.icon" class="nav-icon"></i>
            <span class="nav-label">{{ item.label }}</span>
          </div>
          <div class="nav-indicator"></div>
        </div>
      </div>
    </div>

    <!-- 右侧操作按钮 -->
    <div class="nav-right">
      <NTooltip trigger="hover">
        <template #trigger>
          <NButton
            quaternary
            circle
            size="large"
            class="nav-btn"
            @click="refreshData"
          >
            <template #icon>
              <NIcon size="20">
                <i class="mdi:refresh"></i>
              </NIcon>
            </template>
          </NButton>
        </template>
        刷新数据
      </NTooltip>

      <NTooltip trigger="hover">
        <template #trigger>
          <NButton
            quaternary
            circle
            size="large"
            class="nav-btn"
            @click="toggleFullscreen"
          >
            <template #icon>
              <NIcon size="20">
                <i :class="isFullscreen ? 'mdi:fullscreen-exit' : 'mdi:fullscreen'"></i>
              </NIcon>
            </template>
          </NButton>
        </template>
        {{ isFullscreen ? '退出全屏' : '全屏显示' }}
      </NTooltip>

      <NTooltip trigger="hover">
        <template #trigger>
          <NButton
            quaternary
            circle
            size="large"
            class="nav-btn"
            @click="goBack"
          >
            <template #icon>
              <NIcon size="20">
                <i class="mdi:arrow-left"></i>
              </NIcon>
            </template>
          </NButton>
        </template>
        返回主页
      </NTooltip>
    </div>
  </div>
</template>

<style scoped>
.dashboard-navigation {
  height: 60px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(14, 66, 210, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: relative;
  z-index: 50;
}

.nav-left {
  display: flex;
  align-items: center;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-item {
  position: relative;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.nav-item:hover {
  background: rgba(14, 66, 210, 0.1);
  transform: translateY(-2px);
}

.nav-item.active {
  background: rgba(14, 66, 210, 0.2);
  color: #ffffff;
}

.nav-item.active .nav-indicator {
  transform: scaleX(1);
}

.nav-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 2;
}

.nav-icon {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.nav-item.active .nav-icon {
  color: #ffffff;
}

.nav-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.nav-item.active .nav-label {
  color: #ffffff;
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #0E42D2, #2060F0);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-btn {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  color: #ffffff !important;
  background: rgba(14, 66, 210, 0.1) !important;
  transform: translateY(-2px);
}

/* 动画效果 */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-navigation {
  animation: slideInFromTop 0.6s ease-out;
}
</style>
