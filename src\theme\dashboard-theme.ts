import type { GlobalThemeOverrides } from 'naive-ui';

/** 数据大屏专用主题配置 */
export const dashboardTheme: GlobalThemeOverrides = {
  common: {
    primaryColor: '#0E42D2',
    primaryColorHover: '#1E52E2',
    primaryColorPressed: '#0A35B8',
    primaryColorSuppl: '#2060F0',

    // 背景色
    bodyColor: 'transparent',
    cardColor: 'rgba(255, 255, 255, 0.05)',
    modalColor: 'rgba(255, 255, 255, 0.08)',
    popoverColor: 'rgba(255, 255, 255, 0.08)',

    // 文字颜色
    textColorBase: '#ffffff',
    textColor1: '#ffffff',
    textColor2: 'rgba(255, 255, 255, 0.9)',
    textColor3: 'rgba(255, 255, 255, 0.7)',

    // 边框
    borderColor: 'rgba(14, 66, 210, 0.3)',
    dividerColor: 'rgba(14, 66, 210, 0.2)',

    // 阴影
    boxShadow1: '0 8px 32px rgba(0, 0, 0, 0.3)',
    boxShadow2: '0 0 20px rgba(14, 66, 210, 0.4)'
  },
  Card: {
    color: 'rgba(255, 255, 255, 0.05)',
    colorModal: 'rgba(255, 255, 255, 0.08)',
    borderColor: 'rgba(14, 66, 210, 0.3)',
    borderRadius: '12px'
  },
  DataTable: {
    thColor: 'rgba(255, 255, 255, 0.08)',
    tdColor: 'transparent',
    borderColor: 'rgba(14, 66, 210, 0.2)'
  },
  Button: {
    colorPrimary: '#0E42D2',
    colorHoverPrimary: '#1E52E2',
    colorPressedPrimary: '#0A35B8',
    borderPrimary: '1px solid #0E42D2'
  }
};
