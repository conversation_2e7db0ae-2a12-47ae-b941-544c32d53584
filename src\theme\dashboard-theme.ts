import type { GlobalThemeOverrides } from 'naive-ui';

/** 数据大屏专用主题配置 */
export const dashboardTheme: GlobalThemeOverrides = {
  common: {
    primaryColor: '#0E42D2',
    primaryColorHover: '#1E52E2',
    primaryColorPressed: '#0A35B8',
    primaryColorSuppl: '#2060F0',

    // 背景色
    bodyColor: 'transparent',
    cardColor: 'rgba(255, 255, 255, 0.05)',
    modalColor: 'rgba(255, 255, 255, 0.08)',
    popoverColor: 'rgba(255, 255, 255, 0.08)',

    // 文字颜色
    textColorBase: '#ffffff',
    textColor1: '#ffffff',
    textColor2: 'rgba(255, 255, 255, 0.9)',
    textColor3: 'rgba(255, 255, 255, 0.7)',

    // 边框
    borderColor: 'rgba(14, 66, 210, 0.3)',
    dividerColor: 'rgba(14, 66, 210, 0.2)',

    // 阴影
    boxShadow1: '0 8px 32px rgba(0, 0, 0, 0.3)',
    boxShadow2: '0 0 20px rgba(14, 66, 210, 0.4)',
    boxShadow3: '0 12px 40px rgba(14, 66, 210, 0.2)',

    // 圆角
    borderRadius: '8px',
    borderRadiusSmall: '4px',

    // 字体
    fontFamily:
      '"Inter", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif',
    fontWeight: '400',
    fontWeightStrong: '600'
  },
  Card: {
    color: 'rgba(255, 255, 255, 0.05)',
    colorModal: 'rgba(255, 255, 255, 0.08)',
    colorEmbedded: 'rgba(255, 255, 255, 0.03)',
    borderColor: 'rgba(14, 66, 210, 0.3)',
    borderRadius: '12px',
    paddingMedium: '20px 24px',
    titleFontSizeSmall: '16px',
    titleFontSizeMedium: '18px',
    titleFontSizeLarge: '20px',
    titleTextColor: '#ffffff',
    titleFontWeight: '600'
  },
  DataTable: {
    thColor: 'rgba(255, 255, 255, 0.08)',
    tdColor: 'transparent',
    borderColor: 'rgba(14, 66, 210, 0.2)',
    thTextColor: 'rgba(255, 255, 255, 0.9)',
    tdTextColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: '8px'
  },
  Button: {
    colorPrimary: '#0E42D2',
    colorHoverPrimary: '#1E52E2',
    colorPressedPrimary: '#0A35B8',
    borderPrimary: '1px solid #0E42D2',
    borderHoverPrimary: '1px solid #1E52E2',
    borderPressedPrimary: '1px solid #0A35B8',
    borderRadius: '8px',
    fontWeight: '500',

    // 次要按钮
    colorSecondary: 'rgba(255, 255, 255, 0.08)',
    colorHoverSecondary: 'rgba(255, 255, 255, 0.12)',
    colorPressedSecondary: 'rgba(255, 255, 255, 0.06)',
    borderSecondary: '1px solid rgba(14, 66, 210, 0.3)',

    // 四级按钮
    colorQuaternary: 'transparent',
    colorHoverQuaternary: 'rgba(14, 66, 210, 0.1)',
    colorPressedQuaternary: 'rgba(14, 66, 210, 0.15)'
  },
  Input: {
    color: 'rgba(255, 255, 255, 0.05)',
    colorFocus: 'rgba(255, 255, 255, 0.08)',
    textColor: '#ffffff',
    placeholderColor: 'rgba(255, 255, 255, 0.5)',
    border: '1px solid rgba(14, 66, 210, 0.3)',
    borderHover: '1px solid rgba(14, 66, 210, 0.5)',
    borderFocus: '1px solid #0E42D2',
    borderRadius: '8px'
  },
  Select: {
    peers: {
      InternalSelection: {
        color: 'rgba(255, 255, 255, 0.05)',
        colorActive: 'rgba(255, 255, 255, 0.08)',
        textColor: '#ffffff',
        placeholderColor: 'rgba(255, 255, 255, 0.5)',
        border: '1px solid rgba(14, 66, 210, 0.3)',
        borderHover: '1px solid rgba(14, 66, 210, 0.5)',
        borderActive: '1px solid #0E42D2',
        borderRadius: '8px'
      }
    }
  },
  Menu: {
    color: 'transparent',
    itemColorHover: 'rgba(14, 66, 210, 0.1)',
    itemColorActive: 'rgba(14, 66, 210, 0.2)',
    itemTextColor: 'rgba(255, 255, 255, 0.8)',
    itemTextColorHover: '#ffffff',
    itemTextColorActive: '#ffffff',
    itemIconColor: 'rgba(255, 255, 255, 0.7)',
    itemIconColorHover: '#ffffff',
    itemIconColorActive: '#ffffff',
    borderRadius: '8px'
  },
  Tooltip: {
    color: 'rgba(0, 0, 0, 0.9)',
    textColor: '#ffffff',
    borderRadius: '6px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.4)'
  },
  Dropdown: {
    color: 'rgba(255, 255, 255, 0.08)',
    optionColorHover: 'rgba(14, 66, 210, 0.1)',
    optionColorActive: 'rgba(14, 66, 210, 0.2)',
    optionTextColor: 'rgba(255, 255, 255, 0.8)',
    optionTextColorHover: '#ffffff',
    optionTextColorActive: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
  }
};
